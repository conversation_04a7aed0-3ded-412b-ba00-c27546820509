import React from 'react';
import {
  <PERSON>,
  Container,
  AppBar,
  Too<PERSON><PERSON>,
  Typography,
  IconButton,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Brightness4,
  Brightness7,
} from '@mui/icons-material';
import { observer } from 'mobx-react-lite';
import { useStore } from '../../contexts/StoreContext';
import { Dashboard } from '../pages/Dashboard';

export const MainLayout: React.FC = observer(() => {
  const { uiStore } = useStore();

  const handleThemeToggle = () => {
    uiStore.toggleTheme();
  };

  const handleMenuToggle = () => {
    uiStore.toggleSidebar();
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <IconButton
            size="large"
            edge="start"
            color="inherit"
            aria-label="menu"
            onClick={handleMenuToggle}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
          
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Modern React App
          </Typography>
          
          <FormControlLabel
            control={
              <Switch
                checked={uiStore.themeMode === 'dark'}
                onChange={handleThemeToggle}
                color="default"
              />
            }
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {uiStore.themeMode === 'dark' ? <Brightness7 /> : <Brightness4 />}
                {uiStore.themeMode === 'dark' ? 'Dark' : 'Light'}
              </Box>
            }
          />
        </Toolbar>
      </AppBar>
      
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Dashboard />
      </Container>
    </Box>
  );
});
