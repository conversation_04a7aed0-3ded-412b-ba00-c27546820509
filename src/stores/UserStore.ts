import { makeAutoObservable } from 'mobx';
import type { RootStore } from './RootStore';

export interface User {
  id: string;
  name: string;
  email: string;
}

export class UserStore {
  rootStore: RootStore;
  currentUser: User | null = null;
  isLoading = false;
  error: string | null = null;

  constructor(rootStore: RootStore) {
    makeAutoObservable(this);
    this.rootStore = rootStore;
  }

  setCurrentUser = (user: User | null) => {
    this.currentUser = user;
  };

  setLoading = (loading: boolean) => {
    this.isLoading = loading;
  };

  setError = (error: string | null) => {
    this.error = error;
  };

  // Example async action
  fetchUser = async (userId: string) => {
    this.setLoading(true);
    this.setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const user: User = {
        id: userId,
        name: '<PERSON>',
        email: '<EMAIL>'
      };
      
      this.setCurrentUser(user);
    } catch (error) {
      this.setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      this.setLoading(false);
    }
  };

  logout = () => {
    this.setCurrentUser(null);
    this.setError(null);
  };
}
