import { makeAutoObservable } from 'mobx';
import type { RootStore } from './RootStore';

export type ThemeMode = 'light' | 'dark';

export class UIStore {
  rootStore: RootStore;
  themeMode: ThemeMode = 'light';
  sidebarOpen = false;
  notifications: string[] = [];

  constructor(rootStore: RootStore) {
    makeAutoObservable(this);
    this.rootStore = rootStore;
  }

  setThemeMode = (mode: ThemeMode) => {
    this.themeMode = mode;
  };

  toggleTheme = () => {
    this.themeMode = this.themeMode === 'light' ? 'dark' : 'light';
  };

  setSidebarOpen = (open: boolean) => {
    this.sidebarOpen = open;
  };

  toggleSidebar = () => {
    this.sidebarOpen = !this.sidebarOpen;
  };

  addNotification = (message: string) => {
    this.notifications.push(message);
  };

  removeNotification = (index: number) => {
    this.notifications.splice(index, 1);
  };

  clearNotifications = () => {
    this.notifications = [];
  };
}
