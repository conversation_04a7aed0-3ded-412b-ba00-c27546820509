import React from "react";
import {
    Grid2 as <PERSON><PERSON>,
    Card,
    CardContent,
    <PERSON><PERSON><PERSON>,
    <PERSON>ton,
    Box,
    Chip,
    CircularProgress,
    Al<PERSON>,
} from "@mui/material";
import { Person, Dashboard as DashboardIcon, Settings, Notifications } from "@mui/icons-material";
import { observer } from "mobx-react-lite";
import { useStore } from "../../contexts/StoreContext";

export const Dashboard: React.FC = observer(() => {
    const { userStore, uiStore } = useStore();

    const handleFetchUser = () => {
        userStore.fetchUser("123");
    };

    const handleAddNotification = () => {
        uiStore.addNotification(`New notification at ${new Date().toLocaleTimeString()}`);
    };

    const handleClearNotifications = () => {
        uiStore.clearNotifications();
    };

    return (
        <Box>
            <Typography variant="h4" component="h1" gutterBottom className="mb-6">
                Dashboard
            </Typography>

            <Grid container spacing={3}>
                {/* User Info Card */}
                <Grid xs={12} md={6}>
                    <Card className="h-full">
                        <CardContent>
                            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                                <Person sx={{ mr: 1 }} />
                                <Typography variant="h6">User Information</Typography>
                            </Box>

                            {userStore.isLoading && (
                                <Box sx={{ display: "flex", justifyContent: "center", my: 2 }}>
                                    <CircularProgress />
                                </Box>
                            )}

                            {userStore.error && (
                                <Alert severity="error" sx={{ mb: 2 }}>
                                    {userStore.error}
                                </Alert>
                            )}

                            {userStore.currentUser ? (
                                <Box>
                                    <Typography variant="body1" gutterBottom>
                                        <strong>Name:</strong> {userStore.currentUser.name}
                                    </Typography>
                                    <Typography variant="body1" gutterBottom>
                                        <strong>Email:</strong> {userStore.currentUser.email}
                                    </Typography>
                                    <Typography variant="body1" gutterBottom>
                                        <strong>ID:</strong> {userStore.currentUser.id}
                                    </Typography>
                                    <Button
                                        variant="outlined"
                                        color="secondary"
                                        onClick={userStore.logout}
                                        sx={{ mt: 2 }}
                                    >
                                        Logout
                                    </Button>
                                </Box>
                            ) : (
                                <Box>
                                    <Typography variant="body2" color="text.secondary" gutterBottom>
                                        No user logged in
                                    </Typography>
                                    <Button
                                        variant="contained"
                                        onClick={handleFetchUser}
                                        disabled={userStore.isLoading}
                                        sx={{ mt: 2 }}
                                    >
                                        Load User
                                    </Button>
                                </Box>
                            )}
                        </CardContent>
                    </Card>
                </Grid>

                {/* Theme & UI Card */}
                <Grid item xs={12} md={6}>
                    <Card className="h-full">
                        <CardContent>
                            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                                <Settings sx={{ mr: 1 }} />
                                <Typography variant="h6">UI Settings</Typography>
                            </Box>

                            <Box sx={{ mb: 2 }}>
                                <Typography variant="body2" gutterBottom>
                                    Current Theme:
                                </Typography>
                                <Chip
                                    label={uiStore.themeMode}
                                    color={uiStore.themeMode === "dark" ? "primary" : "secondary"}
                                    variant="outlined"
                                />
                            </Box>

                            <Box sx={{ mb: 2 }}>
                                <Typography variant="body2" gutterBottom>
                                    Sidebar Status:
                                </Typography>
                                <Chip
                                    label={uiStore.sidebarOpen ? "Open" : "Closed"}
                                    color={uiStore.sidebarOpen ? "success" : "default"}
                                    variant="outlined"
                                />
                            </Box>

                            <Button
                                variant="outlined"
                                onClick={uiStore.toggleSidebar}
                                sx={{ mt: 1 }}
                            >
                                Toggle Sidebar
                            </Button>
                        </CardContent>
                    </Card>
                </Grid>

                {/* Notifications Card */}
                <Grid item xs={12}>
                    <Card>
                        <CardContent>
                            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                                <Notifications sx={{ mr: 1 }} />
                                <Typography variant="h6">Notifications</Typography>
                                <Chip
                                    label={uiStore.notifications.length}
                                    color="primary"
                                    size="small"
                                    sx={{ ml: 1 }}
                                />
                            </Box>

                            <Box sx={{ mb: 2, display: "flex", gap: 1 }}>
                                <Button
                                    variant="contained"
                                    size="small"
                                    onClick={handleAddNotification}
                                >
                                    Add Notification
                                </Button>
                                <Button
                                    variant="outlined"
                                    size="small"
                                    onClick={handleClearNotifications}
                                    disabled={uiStore.notifications.length === 0}
                                >
                                    Clear All
                                </Button>
                            </Box>

                            {uiStore.notifications.length === 0 ? (
                                <Typography variant="body2" color="text.secondary">
                                    No notifications
                                </Typography>
                            ) : (
                                <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                                    {uiStore.notifications.map((notification, index) => (
                                        <Alert
                                            key={index}
                                            severity="info"
                                            onClose={() => uiStore.removeNotification(index)}
                                        >
                                            {notification}
                                        </Alert>
                                    ))}
                                </Box>
                            )}
                        </CardContent>
                    </Card>
                </Grid>

                {/* Demo Tailwind Card */}
                <Grid item xs={12}>
                    <Card>
                        <CardContent>
                            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                                <DashboardIcon sx={{ mr: 1 }} />
                                <Typography variant="h6">Tailwind CSS Demo</Typography>
                            </Box>

                            <div className="p-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg text-white">
                                <h3 className="text-xl font-bold mb-2">Tailwind CSS is Working!</h3>
                                <p className="text-blue-100">
                                    This card demonstrates that Tailwind CSS is properly configured
                                    and working alongside Material-UI components.
                                </p>
                                <div className="mt-4 flex gap-2">
                                    <span className="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm">
                                        React
                                    </span>
                                    <span className="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm">
                                        TypeScript
                                    </span>
                                    <span className="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm">
                                        MobX
                                    </span>
                                    <span className="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm">
                                        Material-UI
                                    </span>
                                    <span className="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm">
                                        Tailwind
                                    </span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>
        </Box>
    );
});
