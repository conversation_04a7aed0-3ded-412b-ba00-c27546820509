import React from "react";
import { ThemeProvider } from "@mui/material/styles";
import { CssBaseline } from "@mui/material";
import { observer } from "mobx-react-lite";
import { StoreProvider } from "./contexts/StoreContext";
import { lightTheme, darkTheme } from "./theme/theme";
import { MainLayout } from "./components/layout/MainLayout";
import { useStore } from "./contexts/StoreContext";

const AppContent: React.FC = observer(() => {
    const { uiStore } = useStore();
    const theme = uiStore.themeMode === "light" ? lightTheme : darkTheme;

    return (
        <ThemeProvider theme={theme}>
            <CssBaseline />
            <MainLayout />
        </ThemeProvider>
    );
});

const App: React.FC = () => {
    return (
        <StoreProvider>
            <AppContent />
        </StoreProvider>
    );
};

export default App;
